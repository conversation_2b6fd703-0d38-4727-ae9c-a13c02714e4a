package middleware

import (
	"net/http"
	"strings"

	"stellar-go/internal/auth"
	"stellar-go/internal/config"
	"stellar-go/internal/models"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware creates a JWT authentication middleware
func AuthMiddleware(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get Authorization header
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error: "Authorization header required",
				Code:  "missing_auth_header",
			})
			c.Abort()
			return
		}

		// Check Bearer token format
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error: "Invalid authorization header format",
				Code:  "invalid_auth_format",
			})
			c.Abort()
			return
		}

		// Validate token
		token := parts[1]
		claims, err := jwtManager.ValidateAccessToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error: "Invalid or expired token",
				Code:  "invalid_token",
			})
			c.Abort()
			return
		}

		// Set user context
		c.Set("user_id", claims.UserID)
		c.Set("user_claims", claims)
		c.Next()
	}
}

// AdminMiddleware ensures the user has admin privileges
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// This middleware should be used after AuthMiddleware
		_, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error: "Authentication required",
				Code:  "auth_required",
			})
			c.Abort()
			return
		}

		// Get user from database to check admin status
		// This would typically be done by injecting a user service
		// For now, we'll assume admin check is done in the handler
		c.Set("requires_admin", true)
		c.Next()
	}
}

// CORSMiddleware handles Cross-Origin Resource Sharing
func CORSMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// Check if the origin is allowed
		allowedOrigins := strings.Split(cfg.CORS.AllowedOrigins, ",")
		originAllowed := false
		hasWildcard := false

		for _, allowedOrigin := range allowedOrigins {
			allowedOrigin = strings.TrimSpace(allowedOrigin)
			if allowedOrigin == "*" {
				hasWildcard = true
				originAllowed = true
				break
			}
			if origin != "" && allowedOrigin == origin {
				originAllowed = true
				break
			}
		}

		// Set CORS headers
		if originAllowed {
			if origin != "" {
				c.Header("Access-Control-Allow-Origin", origin)
			} else if hasWildcard {
				c.Header("Access-Control-Allow-Origin", "*")
			}
		}

		// For OPTIONS requests, always set CORS headers if we have allowed origins
		// This handles preflight requests that might not have an Origin header
		if c.Request.Method == "OPTIONS" && (originAllowed || hasWildcard || len(allowedOrigins) > 0) {
			if origin != "" && originAllowed {
				c.Header("Access-Control-Allow-Origin", origin)
			} else if hasWildcard {
				c.Header("Access-Control-Allow-Origin", "*")
			} else if len(allowedOrigins) > 0 && origin == "" {
				// For OPTIONS without Origin, allow the first configured origin
				firstOrigin := strings.TrimSpace(allowedOrigins[0])
				if firstOrigin != "*" {
					c.Header("Access-Control-Allow-Origin", firstOrigin)
				}
			}
		}

		if cfg.CORS.AllowCredentials {
			c.Header("Access-Control-Allow-Credentials", "true")
		}

		c.Header("Access-Control-Allow-Headers", cfg.CORS.AllowedHeaders)
		c.Header("Access-Control-Allow-Methods", cfg.CORS.AllowedMethods)

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// RequestIDMiddleware adds a unique request ID to each request
func RequestIDMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if _, ok := recovered.(string); ok {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error: "Internal server error",
				Code:  "internal_error",
			})
		}
		c.AbortWithStatus(http.StatusInternalServerError)
	})
}

// GetUserID extracts the user ID from the Gin context
func GetUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	
	id, ok := userID.(uint)
	return id, ok
}

// GetUserClaims extracts the JWT claims from the Gin context
func GetUserClaims(c *gin.Context) (*auth.Claims, bool) {
	claims, exists := c.Get("user_claims")
	if !exists {
		return nil, false
	}

	userClaims, ok := claims.(*auth.Claims)
	return userClaims, ok
}

// RequiresAdmin checks if the current request requires admin privileges
func RequiresAdmin(c *gin.Context) bool {
	requiresAdmin, exists := c.Get("requires_admin")
	if !exists {
		return false
	}
	
	required, ok := requiresAdmin.(bool)
	return ok && required
}
