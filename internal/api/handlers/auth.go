package handlers

import (
	"fmt"
	"net/http"
	"net/url"

	"stellar-go/internal/auth"
	"stellar-go/internal/config"
	"stellar-go/internal/models"
	"stellar-go/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	jwtManager  *auth.JWTManager
	oktaManager *auth.OktaManager
	userService *services.UserService
	config      *config.Config
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(jwtManager *auth.JWTManager, oktaManager *auth.OktaManager, userService *services.UserService, cfg *config.Config) *AuthHandler {
	return &AuthHandler{
		jwtManager:  jwtManager,
		oktaManager: oktaManager,
		userService: userService,
		config:      cfg,
	}
}

// Login handles GET /login
// @Summary Initiate Okta login
// @Description Redirects to Okta for authentication
// @Tags Authentication
// @Accept json
// @Produce json
// @Success 301 {string} string "Redirect to Okta login"
// @Router /login [get]
func (h *AuthHandler) Login(c *gin.Context) {
	authURL, _, _, err := h.oktaManager.GenerateAuthURL()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to generate authorization URL",
			Code:  "auth_url_error",
		})
		return
	}

	// Store state and nonce in session/cookie for validation
	// For simplicity, we'll skip this in the example
	// In production, you should store these securely

	// Prevent caching of the redirect
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	c.Redirect(http.StatusFound, authURL)
}

// LoginCallback handles GET /login/callback
// @Summary Handle Okta callback
// @Description Processes the authorization code from Okta and returns JWT tokens
// @Tags Authentication
// @Accept json
// @Produce json
// @Param code query string true "Authorization code from Okta"
// @Param state query string false "State parameter for CSRF protection"
// @Success 301 {string} string "Redirect to frontend with tokens"
// @Failure 403 {object} models.ErrorResponse
// @Router /login/callback [get]
func (h *AuthHandler) LoginCallback(c *gin.Context) {
	code := c.Query("code")
	state := c.Query("state")

	if code == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Authorization code is required",
			Code:  "missing_code",
		})
		return
	}

	// Exchange code for tokens
	tokenResponse, err := h.oktaManager.ExchangeCodeForTokens(code, state)
	if err != nil {
		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error: "Failed to exchange authorization code",
			Code:  "token_exchange_error",
		})
		return
	}

	// Get user info from Okta
	userInfo, err := h.oktaManager.GetUserInfo(tokenResponse.AccessToken)
	if err != nil {
		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error: "Failed to get user information",
			Code:  "user_info_error",
		})
		return
	}

	// Create or update user in database
	user, err := h.userService.CreateOrUpdateUserFromOkta(
		userInfo.Email,
		userInfo.FirstName,
		userInfo.LastName,
		userInfo.IsAdmin,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to create/update user",
			Code:  "user_creation_error",
		})
		return
	}

	// Generate internal JWT tokens
	accessToken, refreshToken, err := h.jwtManager.GenerateTokenPair(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to generate tokens",
			Code:  "token_generation_error",
		})
		return
	}

	// Redirect to frontend with tokens
	frontendURL := h.config.JWT.FrontendURL
	redirectURL := fmt.Sprintf("%s?access_token=%s&refresh_token=%s",
		frontendURL,
		url.QueryEscape(accessToken),
		url.QueryEscape(refreshToken),
	)

	c.Redirect(http.StatusMovedPermanently, redirectURL)
}

// RefreshToken handles POST /auth/refresh
// @Summary Refresh JWT tokens
// @Description Exchange refresh token for new access and refresh tokens
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body models.RefreshTokenRequest true "Refresh token request"
// @Success 200 {object} models.TokenResponse
// @Failure 400 {object} models.ErrorResponse
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req models.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid request body",
			Code:  "invalid_request",
		})
		return
	}

	// Refresh tokens
	newAccessToken, newRefreshToken, err := h.jwtManager.RefreshTokens(req.Access, req.Refresh)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid refresh token",
			Code:  "invalid_refresh_token",
		})
		return
	}

	response := models.TokenResponse{
		Access:  newAccessToken,
		Refresh: newRefreshToken,
	}

	c.JSON(http.StatusOK, response)
}
