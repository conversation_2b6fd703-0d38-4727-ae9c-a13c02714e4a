package auth

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"stellar-go/internal/config"
)

// OktaManager handles Okta OIDC integration (no Management API)
type OktaManager struct {
	config      *config.OktaConfig
	redirectURI string
	httpClient  *http.Client
}

// OktaUserInfo represents user information from Okta
type OktaUserInfo struct {
	ID        string
	Email     string
	FirstName string
	LastName  string
	IsAdmin   bool
}

// NewOktaManager creates a new Okta manager for OIDC authentication only
func NewOktaManager(cfg *config.Config) (*OktaManager, error) {
	return &OktaManager{
		config:      &cfg.Okta,
		redirectURI: cfg.Okta.RedirectURI,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

// GenerateAuthURL generates the Okta authorization URL
func (o *OktaManager) GenerateAuthURL() (string, string, string, error) {
	// Generate random state and nonce
	state, err := generateRandomString(32)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to generate state: %w", err)
	}

	nonce, err := generateRandomString(32)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Build authorization URL
	baseURL := fmt.Sprintf("https://%s/oauth2/default/v1/authorize", o.config.Domain)
	params := url.Values{
		"client_id":     {o.config.ClientID},
		"response_type": {"code"},
		"scope":         {"openid profile email"},
		"redirect_uri":  {o.redirectURI},
		"state":         {state},
		"nonce":         {nonce},
	}

	authURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())
	return authURL, state, nonce, nil
}

// ExchangeCodeForTokens exchanges authorization code for tokens
func (o *OktaManager) ExchangeCodeForTokens(code, state string) (*OktaTokenResponse, error) {
	tokenURL := fmt.Sprintf("https://%s/oauth2/default/v1/token", o.config.Domain)

	data := url.Values{
		"grant_type":    {"authorization_code"},
		"client_id":     {o.config.ClientID},
		"client_secret": {o.config.ClientSecret},
		"code":          {code},
		"redirect_uri":  {o.redirectURI},
	}

	req, err := http.NewRequest("POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create token request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json")

	resp, err := o.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code for tokens: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("token exchange failed with status %d: %s", resp.StatusCode, string(body))
	}

	var tokenResponse OktaTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResponse); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	return &tokenResponse, nil
}

// GetUserInfo retrieves user information from Okta's userinfo endpoint
func (o *OktaManager) GetUserInfo(accessToken string) (*OktaUserInfo, error) {
	userInfoURL := fmt.Sprintf("https://%s/oauth2/default/v1/userinfo", o.config.Domain)

	req, err := http.NewRequest("GET", userInfoURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create userinfo request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Accept", "application/json")

	resp, err := o.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("userinfo request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var userInfo struct {
		Sub               string   `json:"sub"`
		Email             string   `json:"email"`
		GivenName         string   `json:"given_name"`
		FamilyName        string   `json:"family_name"`
		Groups            []string `json:"groups"`
		PreferredUsername string   `json:"preferred_username"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to decode userinfo response: %w", err)
	}

	// Check if user is admin based on group membership
	isAdmin := false
	if o.config.AdminGroup != "" {
		for _, group := range userInfo.Groups {
			if group == o.config.AdminGroup {
				isAdmin = true
				break
			}
		}
	}

	return &OktaUserInfo{
		ID:        userInfo.Sub,
		Email:     userInfo.Email,
		FirstName: userInfo.GivenName,
		LastName:  userInfo.FamilyName,
		IsAdmin:   isAdmin,
	}, nil
}



// OktaTokenResponse represents the token response from Okta OIDC
type OktaTokenResponse struct {
	AccessToken  string `json:"access_token"`
	IDToken      string `json:"id_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
}

// generateRandomString generates a cryptographically secure random string
func generateRandomString(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes)[:length], nil
}

// ValidateState validates the state parameter for CSRF protection
func ValidateState(receivedState, expectedState string) bool {
	return receivedState == expectedState && receivedState != ""
}

// ParseEmailDomain extracts the domain from an email address
func ParseEmailDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return ""
	}
	return parts[1]
}
